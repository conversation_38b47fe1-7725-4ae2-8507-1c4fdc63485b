import { Button } from '@/components/shadcnui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/shadcnui/card';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { useLoginForm } from '@/contexts/auth/hooks/login-form.hook';
import { useLoginMutation } from '@/contexts/auth/hooks/login-mutation.hook';
import { CircularProgress, Image } from '@nextui-org/react';
import { motion } from 'framer-motion';
import { LucideEye, LucideEyeOff, LucideShield, LucideMonitor } from 'lucide-react';
import { useState } from 'react';

export function Login() {
	const methods = useLoginForm();
	const { login, isLoading } = useLoginMutation();
	const [isVisible, setIsVisible] = useState(false);
	const [loginType, setLoginType] = useState<'admin' | 'device'>('admin');

	function togglePasswordVisibility() {
		setIsVisible(!isVisible);
	}

	const containerVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.6,
				ease: 'easeOut',
				staggerChildren: 0.1,
			},
		},
	};

	const itemVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: {
			opacity: 1,
			y: 0,
			transition: { duration: 0.4, ease: 'easeOut' },
		},
	};

	function renderLoginForm() {
		if (loginType === 'device') {
			return (
				<motion.form
					variants={itemVariants}
					className="space-y-6"
					onSubmit={(e) => {
						e.preventDefault();
						// Implementar lógica de login por token
					}}
				>
					<div className="space-y-2">
						<Label htmlFor="token" className="text-sm font-medium text-foreground">
							Token do Dispositivo
						</Label>
						<div className="relative">
							<LucideMonitor className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
							<Input
								id="token"
								type="text"
								placeholder="Digite o token do dispositivo"
								className="h-12 border-border/50 bg-background/50 pl-10 transition-all duration-200 focus:border-primary/50"
								autoComplete="off"
							/>
						</div>
					</div>

					<Button
						type="submit"
						className="h-12 w-full bg-primary font-medium text-primary-foreground shadow-lg transition-all duration-200 hover:bg-primary/90 hover:shadow-xl"
						disabled={isLoading}
					>
						{isLoading ? (
							<CircularProgress size="sm" color="primary" />
						) : (
							<>
								<LucideMonitor className="mr-2 h-4 w-4" />
								Conectar Dispositivo
							</>
						)}
					</Button>
				</motion.form>
			);
		}

		return (
			<motion.form
				variants={itemVariants}
				onSubmit={methods.handleSubmit((data) => {
					login(data);
				})}
				className="space-y-6"
			>
				<div className="space-y-2">
					<Label htmlFor="user" className="text-sm font-medium text-foreground">
						Usuário
					</Label>
					<div className="relative">
						<LucideShield className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
						<Input
							id="user"
							{...methods.register('user')}
							type="text"
							placeholder="Digite seu usuário"
							className="h-12 border-border/50 bg-background/50 pl-10 transition-all duration-200 focus:border-primary/50"
							autoComplete="username"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<Label htmlFor="password" className="text-sm font-medium text-foreground">
						Senha
					</Label>
					<div className="relative">
						<Input
							id="password"
							{...methods.register('password')}
							type={isVisible ? 'text' : 'password'}
							placeholder="Digite sua senha"
							className="h-12 border-border/50 bg-background/50 pr-10 transition-all duration-200 focus:border-primary/50"
							autoComplete="current-password"
							maxLength={25}
						/>
						<Button
							type="button"
							variant="ghost"
							size="icon"
							onClick={togglePasswordVisibility}
							className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 text-muted-foreground transition-colors hover:text-foreground"
						>
							{isVisible ? <LucideEyeOff className="h-4 w-4" /> : <LucideEye className="h-4 w-4" />}
						</Button>
					</div>
				</div>

				<Button
					type="submit"
					className="h-12 w-full bg-primary font-medium text-primary-foreground shadow-lg transition-all duration-200 hover:bg-primary/90 hover:shadow-xl"
					disabled={isLoading}
				>
					{isLoading ? (
						<CircularProgress size="sm" color="current" />
					) : (
						<>
							<LucideShield className="mr-2 h-4 w-4" />
							Acessar Sistema
						</>
					)}
				</Button>
			</motion.form>
		);
	}

	return (
		<div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-background/95 p-4">
			{/* Background Pattern */}
			<div className="pointer-events-none absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_50%)]" />
			<div className="pointer-events-none absolute inset-0 bg-[linear-gradient(to_right,transparent_0%,rgba(34,197,94,0.05)_50%,transparent_100%)]" />

			<motion.div
				variants={containerVariants}
				initial="hidden"
				animate="visible"
				className="mx-auto grid w-full max-w-6xl items-center gap-8 lg:grid-cols-2 lg:gap-16"
			>
				{/* Login Card */}
				<motion.div variants={itemVariants} className="mx-auto w-full max-w-md lg:mx-0">
					<Card className="border-border/50 bg-card/80 shadow-2xl backdrop-blur-xl">
						<CardHeader className="space-y-6 pb-8">
							<motion.div variants={itemVariants} className="flex justify-center">
								<Image src="/assets/svgs/logo.svg" alt="StreamHub Logo" className="h-12 w-auto" />
							</motion.div>

							<motion.div variants={itemVariants} className="space-y-2 text-center">
								<h1 className="text-2xl font-bold text-foreground">Bem-vindo de volta</h1>
								<p className="text-muted-foreground">Faça login para acessar sua conta</p>
							</motion.div>

							{/* Login Type Selector */}
							<motion.div variants={itemVariants} className="flex rounded-lg bg-muted/50 p-1">
								<button
									type="button"
									onClick={() => setLoginType('admin')}
									className={`flex flex-1 items-center justify-center gap-2 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200 ${
										loginType === 'admin'
											? 'bg-primary text-primary-foreground shadow-sm'
											: 'text-muted-foreground hover:text-foreground'
									}`}
								>
									<LucideShield className="h-4 w-4" />
									Administrador
								</button>
								<button
									type="button"
									onClick={() => setLoginType('device')}
									className={`flex flex-1 items-center justify-center gap-2 rounded-md px-4 py-2 text-sm font-medium transition-all duration-200 ${
										loginType === 'device'
											? 'bg-primary text-primary-foreground shadow-sm'
											: 'text-muted-foreground hover:text-foreground'
									}`}
								>
									<LucideMonitor className="h-4 w-4" />
									Dispositivo
								</button>
							</motion.div>
						</CardHeader>

						<CardContent className="pb-8">{renderLoginForm()}</CardContent>
					</Card>
				</motion.div>

				{/* Illustration */}
				<motion.div variants={itemVariants} className="hidden items-center justify-center lg:flex">
					<div className="relative">
						<div className="absolute inset-0 rounded-full bg-primary/20 blur-3xl" />
						<Image
							src="/assets/svgs/illustration.svg"
							alt="StreamHub Illustration"
							className="relative z-10 h-auto w-full max-w-lg drop-shadow-2xl"
						/>
					</div>
				</motion.div>
			</motion.div>
		</div>
	);
}
