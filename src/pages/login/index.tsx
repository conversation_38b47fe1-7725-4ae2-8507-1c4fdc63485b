import { Button } from '@/components/shadcnui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/shadcnui/card';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { useLoginForm } from '@/contexts/auth/hooks/login-form.hook';
import { useLoginMutation } from '@/contexts/auth/hooks/login-mutation.hook';
import { motion } from 'framer-motion';
import { LucideEye, LucideEyeOff, LucideShield, LucideMonitor, LucideLoader2, LucideSparkles, LucideGlobe } from 'lucide-react';
import { useState } from 'react';

export function Login() {
	const methods = useLoginForm();
	const { login, isLoading } = useLoginMutation();
	const [isVisible, setIsVisible] = useState(false);
	const [loginType, setLoginType] = useState<'admin' | 'device'>('admin');

	function togglePasswordVisibility() {
		setIsVisible(!isVisible);
	}

	const containerVariants = {
		hidden: { opacity: 0, y: 20 },
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.8,
				ease: 'easeOut',
				staggerChildren: 0.15,
			},
		},
	};

	const itemVariants = {
		hidden: { opacity: 0, y: 30 },
		visible: {
			opacity: 1,
			y: 0,
			transition: { duration: 0.6, ease: 'easeOut' },
		},
	};

	const floatingVariants = {
		animate: {
			y: [-10, 10, -10],
			transition: {
				duration: 6,
				repeat: Infinity,
				ease: 'easeInOut',
			},
		},
	};

	function renderLoginForm() {
		if (loginType === 'device') {
			return (
				<motion.form
					variants={itemVariants}
					className="space-y-6"
					onSubmit={(e) => {
						e.preventDefault();
						// Implementar lógica de login por token
					}}
				>
					<div className="space-y-3">
						<Label htmlFor="token" className="flex items-center gap-2 text-sm font-semibold text-foreground">
							<LucideMonitor className="h-4 w-4 text-primary" />
							Token do Dispositivo
						</Label>
						<div className="group relative">
							<Input
								id="token"
								type="text"
								placeholder="Digite o token do dispositivo"
								className="h-14 border-2 border-border/30 bg-background/60 text-base backdrop-blur-sm transition-all duration-300 focus:border-primary/60 focus:bg-background/80 focus:shadow-lg focus:shadow-primary/10 group-hover:border-border/50"
								autoComplete="off"
							/>
							<div className="pointer-events-none absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
						</div>
					</div>

					<Button
						type="submit"
						className="h-14 w-full transform bg-gradient-to-r from-primary to-primary/80 text-base font-semibold text-primary-foreground shadow-xl transition-all duration-300 hover:scale-[1.02] hover:from-primary/90 hover:to-primary/70 hover:shadow-2xl hover:shadow-primary/25 active:scale-[0.98]"
						disabled={isLoading}
					>
						{isLoading ? (
							<div className="flex items-center gap-2">
								<LucideLoader2 className="h-5 w-5 animate-spin" />
								Conectando...
							</div>
						) : (
							<div className="flex items-center gap-2">
								<LucideMonitor className="h-5 w-5" />
								Conectar Dispositivo
								<LucideSparkles className="ml-1 h-4 w-4" />
							</div>
						)}
					</Button>
				</motion.form>
			);
		}

		return (
			<motion.form
				variants={itemVariants}
				onSubmit={methods.handleSubmit((data) => {
					login(data);
				})}
				className="space-y-6"
			>
				<div className="space-y-3">
					<Label htmlFor="user" className="flex items-center gap-2 text-sm font-semibold text-foreground">
						<LucideShield className="h-4 w-4 text-primary" />
						Usuário
					</Label>
					<div className="group relative">
						<Input
							id="user"
							{...methods.register('user')}
							type="text"
							placeholder="Digite seu usuário"
							className="h-14 border-2 border-border/30 bg-background/60 text-base backdrop-blur-sm transition-all duration-300 focus:border-primary/60 focus:bg-background/80 focus:shadow-lg focus:shadow-primary/10 group-hover:border-border/50"
							autoComplete="username"
						/>
						<div className="pointer-events-none absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
					</div>
				</div>

				<div className="space-y-3">
					<Label htmlFor="password" className="flex items-center gap-2 text-sm font-semibold text-foreground">
						<LucideShield className="h-4 w-4 text-primary" />
						Senha
					</Label>
					<div className="group relative">
						<Input
							id="password"
							{...methods.register('password')}
							type={isVisible ? 'text' : 'password'}
							placeholder="Digite sua senha"
							className="h-14 border-2 border-border/30 bg-background/60 pr-12 text-base backdrop-blur-sm transition-all duration-300 focus:border-primary/60 focus:bg-background/80 focus:shadow-lg focus:shadow-primary/10 group-hover:border-border/50"
							autoComplete="current-password"
							maxLength={25}
						/>
						<Button
							type="button"
							variant="ghost"
							size="icon"
							onClick={togglePasswordVisibility}
							className="absolute right-2 top-1/2 h-10 w-10 -translate-y-1/2 text-muted-foreground transition-all duration-200 hover:bg-primary/10 hover:text-foreground"
						>
							{isVisible ? <LucideEyeOff className="h-5 w-5" /> : <LucideEye className="h-5 w-5" />}
						</Button>
						<div className="pointer-events-none absolute inset-0 rounded-md bg-gradient-to-r from-primary/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
					</div>
				</div>

				<Button
					type="submit"
					className="h-14 w-full transform bg-gradient-to-r from-primary to-primary/80 text-base font-semibold text-primary-foreground shadow-xl transition-all duration-300 hover:scale-[1.02] hover:from-primary/90 hover:to-primary/70 hover:shadow-2xl hover:shadow-primary/25 active:scale-[0.98]"
					disabled={isLoading}
				>
					{isLoading ? (
						<div className="flex items-center gap-2">
							<LucideLoader2 className="h-5 w-5 animate-spin" />
							Entrando...
						</div>
					) : (
						<div className="flex items-center gap-2">
							<LucideShield className="h-5 w-5" />
							Acessar Sistema
							<LucideSparkles className="ml-1 h-4 w-4" />
						</div>
					)}
				</Button>
			</motion.form>
		);
	}

	return (
		<div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-background via-background/95 to-background/90">
			{/* Animated Background Elements */}
			<div className="pointer-events-none absolute inset-0 overflow-hidden">
				{/* Primary gradient orbs */}
				<motion.div
					variants={floatingVariants}
					animate="animate"
					className="absolute left-20 top-20 h-72 w-72 rounded-full bg-primary/10 blur-3xl"
				/>
				<motion.div
					variants={floatingVariants}
					animate="animate"
					style={{ animationDelay: '2s' }}
					className="absolute bottom-20 right-20 h-96 w-96 rounded-full bg-primary/5 blur-3xl"
				/>
				<motion.div
					variants={floatingVariants}
					animate="animate"
					style={{ animationDelay: '4s' }}
					className="bg-primary/8 absolute left-1/2 top-1/2 h-64 w-64 -translate-x-1/2 -translate-y-1/2 transform rounded-full blur-2xl"
				/>

				{/* Grid pattern */}
				<div className="absolute inset-0 bg-[linear-gradient(rgba(34,197,94,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(34,197,94,0.03)_1px,transparent_1px)] bg-[size:50px_50px]" />
			</div>

			<div className="relative z-10 flex min-h-screen items-center justify-center p-4">
				<motion.div
					variants={containerVariants}
					initial="hidden"
					animate="visible"
					className="mx-auto grid w-full max-w-7xl items-center gap-12 lg:grid-cols-2 lg:gap-20"
				>
					{/* Login Card */}
					<motion.div variants={itemVariants} className="mx-auto w-full max-w-md lg:mx-0">
						<Card className="overflow-hidden border-2 border-border/20 bg-card/40 shadow-2xl shadow-black/10 backdrop-blur-2xl">
							{/* Card glow effect */}
							<div className="pointer-events-none absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5" />

							<CardHeader className="relative space-y-8 pb-8">
								<motion.div variants={itemVariants} className="flex justify-center">
									<div className="relative">
										<img src="/assets/svgs/logo.svg" alt="StreamHub Logo" className="h-16 w-auto drop-shadow-lg" />
										<div className="absolute inset-0 scale-150 rounded-full bg-primary/20 blur-xl" />
									</div>
								</motion.div>

								<motion.div variants={itemVariants} className="space-y-3 text-center">
									<h1 className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-3xl font-bold text-transparent">
										Bem-vindo de volta
									</h1>
									<p className="text-base text-muted-foreground">Faça login para acessar sua conta</p>
								</motion.div>

								{/* Login Type Selector */}
								<motion.div
									variants={itemVariants}
									className="flex rounded-xl border border-border/20 bg-muted/30 p-1.5 backdrop-blur-sm"
								>
									<button
										type="button"
										onClick={() => setLoginType('admin')}
										className={`flex flex-1 items-center justify-center gap-2 rounded-lg px-4 py-3 text-sm font-semibold transition-all duration-300 ${
											loginType === 'admin'
												? 'scale-105 bg-primary text-primary-foreground shadow-lg shadow-primary/25'
												: 'text-muted-foreground hover:bg-background/50 hover:text-foreground'
										}`}
									>
										<LucideShield className="h-4 w-4" />
										Administrador
									</button>
									<button
										type="button"
										onClick={() => setLoginType('device')}
										className={`flex flex-1 items-center justify-center gap-2 rounded-lg px-4 py-3 text-sm font-semibold transition-all duration-300 ${
											loginType === 'device'
												? 'scale-105 bg-primary text-primary-foreground shadow-lg shadow-primary/25'
												: 'text-muted-foreground hover:bg-background/50 hover:text-foreground'
										}`}
									>
										<LucideMonitor className="h-4 w-4" />
										Dispositivo
									</button>
								</motion.div>
							</CardHeader>

							<CardContent className="relative pb-8">{renderLoginForm()}</CardContent>
						</Card>
					</motion.div>

					{/* Illustration */}
					<motion.div variants={itemVariants} className="hidden items-center justify-center lg:flex">
						<div className="relative">
							{/* Glow effects */}
							<motion.div
								variants={floatingVariants}
								animate="animate"
								className="absolute inset-0 scale-110 rounded-full bg-primary/15 blur-3xl"
							/>
							<motion.div
								variants={floatingVariants}
								animate="animate"
								style={{ animationDelay: '1s' }}
								className="absolute inset-0 scale-125 rounded-full bg-primary/10 blur-2xl"
							/>

							{/* Main illustration */}
							<motion.img
								variants={floatingVariants}
								animate="animate"
								src="/assets/svgs/illustration.svg"
								alt="StreamHub Illustration"
								className="relative z-10 h-auto w-full max-w-2xl drop-shadow-2xl"
							/>

							{/* Decorative elements */}
							<motion.div
								variants={floatingVariants}
								animate="animate"
								style={{ animationDelay: '3s' }}
								className="absolute -right-4 -top-4 h-8 w-8 rounded-full bg-primary/20 blur-sm"
							/>
							<motion.div
								variants={floatingVariants}
								animate="animate"
								style={{ animationDelay: '5s' }}
								className="absolute -bottom-6 -left-6 h-12 w-12 rounded-full bg-primary/15 blur-md"
							/>
						</div>
					</motion.div>
				</motion.div>
			</div>
		</div>
	);
}
