# UserDropdown Component

## Visão Geral

O `UserDropdown` é um componente React que exibe um dropdown com informações detalhadas do usuário logado, seguindo os princípios SOLID e boas práticas de desenvolvimento.

## Características

### ✅ O que está bom:

- **Responsabilidade única**: Foca apenas na exibição do dropdown do usuário
- **Separação de lógica**: Toda lógica de negócio está separada em hooks customizados
- **Extensibilidade**: Permite configuração de quais informações exibir
- **Tipagem forte**: Interface bem definida com TypeScript
- **Acessibilidade**: Implementa aria-labels e estados disabled
- **Performance**: Usa useMemo para otimizar renderizações

### 🔧 O que pode ser melhorado:

- Adicionar testes unitários
- Implementar funcionalidade real de "Ajuda & Suporte"
- Adicionar animações de entrada/saída
- Criar variantes de tema (claro/escuro)

### 🚀 Próximos passos:

1. Criar testes com Jest e Testing Library
2. Adicionar Storybook para documentação visual
3. Implementar sistema de temas
4. Adicionar suporte a internacionalização (i18n)

## Estrutura de Arquivos

```
user-dropdown/
├── user-dropdown.tsx          # Componente principal
├── types/
│   └── user-dropdown.types.ts # Interfaces TypeScript
├── utils/
│   └── user-display.utils.ts  # Funções utilitárias
└── hooks/
    ├── use-user-info.hook.ts           # Hook para dados do usuário
    └── use-user-dropdown-actions.hook.ts # Hook para ações
```

## Uso

```tsx
import { UserDropdown } from './components/nav-actions/user-dropdown';

// Uso básico
<UserDropdown />

// Com className personalizada
<UserDropdown className="custom-styles" />
```

## Dependências

- `@nextui-org/react` - Avatar component
- `jotai` - Estado global do usuário
- `lucide-react` - Ícones
- `@/components/shadcnui/dropdown-menu` - Componente base do dropdown

## Hooks Utilizados

### `useUserInfo`

Gerencia as informações do usuário e permite configurar quais dados exibir.

### `useUserDropdownActions`

Centraliza as ações do dropdown (logout, ajuda) com tratamento de estados de loading.

## Funções Utilitárias

### `formatUserInfo`

Formata as informações do usuário baseado na configuração.

### `getDisplayName`

Determina qual nome exibir (social ou completo).

### `formatRolesText`

Formata o texto de permissões com pluralização correta.
