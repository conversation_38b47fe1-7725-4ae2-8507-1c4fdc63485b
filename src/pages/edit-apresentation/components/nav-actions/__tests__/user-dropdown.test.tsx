/**
 * Exemplo de teste para o componente UserDropdown
 * Este arquivo serve como referência para implementação futura
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'jotai';
import { UserDropdown } from './user-dropdown';
import { userState } from '@/contexts/auth/states/user.state';

// Mock dos hooks
jest.mock('../../hooks/user/use-user-dropdown-actions.hook', () => ({
	useUserDropdownActions: () => ({
		handleLogout: jest.fn(),
		handleHelp: jest.fn(),
		isLoggingOut: false,
	}),
}));

const mockUser = {
	name: '<PERSON>',
	socialName: null,
	email: '<EMAIL>',
	document: '12345678901',
	time: 'team-a',
	system: 1,
	loginId: 123,
	roles: [1, 2],
	additionalData: {
		nome: '<PERSON>',
		email: '<EMAIL>',
		usuario: 'joao.silva',
		setor: 'TI',
		cargo: 'Desenvolvedor',
		unidade: 'São Paulo',
		empresa: 1,
		matricula: 12345,
	},
	issuedAt: Date.now(),
	expiresAt: Date.now() + 3600000,
};

describe('UserDropdown', () => {
	it('deve renderizar informações do usuário', () => {
		const store = createStore();
		store.set(userState, mockUser);

		render(
			<Provider store={store}>
				<UserDropdown />
			</Provider>,
		);

		expect(screen.getByText('João Silva')).toBeInTheDocument();
		expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
	});

	it('deve exibir cargo e departamento quando disponíveis', () => {
		const store = createStore();
		store.set(userState, mockUser);

		render(
			<Provider store={store}>
				<UserDropdown />
			</Provider>,
		);

		fireEvent.click(screen.getByRole('button'));

		expect(screen.getByText('Desenvolvedor')).toBeInTheDocument();
		expect(screen.getByText('TI')).toBeInTheDocument();
		expect(screen.getByText('São Paulo')).toBeInTheDocument();
	});

	it('deve chamar handleLogout quando clicar em Sair', async () => {
		const mockHandleLogout = jest.fn();
		jest.mocked(useUserDropdownActions).mockReturnValue({
			handleLogout: mockHandleLogout,
			handleHelp: jest.fn(),
			isLoggingOut: false,
		});

		const store = createStore();
		store.set(userState, mockUser);

		render(
			<Provider store={store}>
				<UserDropdown />
			</Provider>,
		);

		fireEvent.click(screen.getByRole('button'));
		fireEvent.click(screen.getByText('Sair'));

		await waitFor(() => {
			expect(mockHandleLogout).toHaveBeenCalledTimes(1);
		});
	});

	it('não deve renderizar quando usuário não estiver logado', () => {
		const store = createStore();
		store.set(userState, null);

		const { container } = render(
			<Provider store={store}>
				<UserDropdown />
			</Provider>,
		);

		expect(container.firstChild).toBeNull();
	});
});
